Field name
Type
Mode
Key
Collation
Default Value
Policy Tags 
Description
id
INTEGER	NULLABLE	-	-	-	
-	
-
tag
INTEGER	REPEATED	-	-	-	
-	
-
line
INTEGER	NULLABLE	-	-	-	
-	
-
isCOD
BOOLEAN	NULLABLE	-	-	-	
-	
-
amount
FLOAT	NULLABLE	-	-	-	
-	
-
freeze
INTEGER	NULLABLE	-	-	-	
-	
-
lineid
STRING	NULLABLE	-	-	-	
-	
-
number
STRING	NULLABLE	-	-	-	
-	
-
remark
STRING	NULLABLE	-	-	-	
-	
-
status
STRING	NULLABLE	-	-	-	
-	
-
vattype
INTEGER	NULLABLE	-	-	-	
-	
-
version
INTEGER	NULLABLE	-	-	-	
-	
-
discount
STRING	NULLABLE	-	-	-	
-	
-
createdby
INTEGER	NULLABLE	-	-	-	
-	
-
ordertype
INTEGER	NULLABLE	-	-	-	
-	
-
reference
STRING	NULLABLE	-	-	-	
-	
-
sharelink
INTEGER	NULLABLE	-	-	-	
-	
-
vatamount
FLOAT	NULLABLE	-	-	-	
-	
-
customerid
STRING	NULLABLE	-	-	-	
-	
-
facebookid
STRING	NULLABLE	-	-	-	
-	
-
trackingno
STRING	NULLABLE	-	-	-	
-	
-
vatpercent
FLOAT	NULLABLE	-	-	-	
-	
-
description
STRING	NULLABLE	-	-	-	
-	
-
shippingvat
INTEGER	NULLABLE	-	-	-	
-	
-
createuserid
INTEGER	NULLABLE	-	-	-	
-	
-
customercode
STRING	NULLABLE	-	-	-	
-	
-
customername
STRING	NULLABLE	-	-	-	
-	
-
facebookname
INTEGER	NULLABLE	-	-	-	
-	
-
saleschannel
STRING	NULLABLE	-	-	-	
-	
-
shippingname
STRING	NULLABLE	-	-	-	
-	
-
amount_pretax
INTEGER	NULLABLE	-	-	-	
-	
-
customeremail
STRING	NULLABLE	-	-	-	
-	
-
customerphone
STRING	NULLABLE	-	-	-	
-	
-
paymentamount
FLOAT	NULLABLE	-	-	-	
-	
-
paymentmethod
STRING	NULLABLE	-	-	-	
-	
-
paymentstatus
STRING	NULLABLE	-	-	-	
-	
-
remark_status
STRING	NULLABLE	-	-	-	
-	
-
return_status
STRING	NULLABLE	-	-	-	
-	
-
shippingemail
STRING	NULLABLE	-	-	-	
-	
-
shippingphone
STRING	NULLABLE	-	-	-	
-	
-
voucheramount
FLOAT	NULLABLE	-	-	-	
-	
-
warehousecode
STRING	NULLABLE	-	-	-	
-	
-
createusername
STRING	NULLABLE	-	-	-	
-	
-
discountamount
FLOAT	NULLABLE	-	-	-	
-	
-
sellerdiscount
FLOAT	NULLABLE	-	-	-	
-	
-
shippingamount
FLOAT	NULLABLE	-	-	-	
-	
-
customeraddress
STRING	NULLABLE	-	-	-	
-	
-
