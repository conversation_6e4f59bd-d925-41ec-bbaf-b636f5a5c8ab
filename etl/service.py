import io

from google.cloud import bigquery

# Construct a BigQuery client object.
client = bigquery.Client()

table_id = "dobybot.import_by_python.localhost_1"

job_config = bigquery.LoadJobConfig(
    schema=[
        bigquery.<PERSON>hema<PERSON><PERSON>("name", "STRING"),
        bigquery.<PERSON>hemaField("post_abbr", "STRING"),
    ],
)

body = io.BytesIO(b"Washington,WA")
client.load_table_from_file(body, table_id, job_config=job_config).result()
previous_rows = client.get_table(table_id).num_rows
assert previous_rows > 0

job_config = bigquery.LoadJobConfig(
    write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
    source_format=bigquery.SourceFormat.NEWLINE_DELIMITED_JSON,
)

uri = "gs://cloud-samples-data/bigquery/us-states/us-states.json"
load_job = client.load_table_from_dataframe(
    uri, table_id, job_config=job_config
)  # Make an API request.

load_job.result()  # Waits for the job to complete.

destination_table = client.get_table(table_id)
print("Loaded {} rows.".format(destination_table.num_rows))
